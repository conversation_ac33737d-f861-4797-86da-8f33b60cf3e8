#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化的缓存系统

验证：
1. 文件路径错误修复
2. 内存优先缓存策略
3. 批量持久化功能
4. 性能改进

作者: Assistant
日期: 2024
"""

import time
import shutil
from pathlib import Path
from typing import List, Dict, Any
from optimized_cache_manager import OptimizedBatchCacheManager
from cache_manager import BatchCacheManager

def test_file_path_bug_fix():
    """测试文件路径错误修复"""
    print("🐛 测试文件路径错误修复...")
    
    # 测试有问题的路径配置
    problematic_cache_dir = Path("cache/brain_api")
    
    # 原始缓存管理器（有bug）
    print("\n📁 原始缓存管理器:")
    try:
        old_cache = BatchCacheManager(cache_dir=problematic_cache_dir, enable_file_cache=True)
        old_cache.set("clickhouse:test", {"test": "data"})
        
        print(f"  Brain API文件: {old_cache.brain_api_cache_file}")
        print(f"  ClickHouse文件: {old_cache.clickhouse_cache_file}")
        print(f"  ClickHouse文件存在: {old_cache.clickhouse_cache_file.exists()}")
        
        # 检查错误的文件路径
        wrong_path = problematic_cache_dir / "clickhouse_cache.pkl"
        print(f"  错误路径存在: {wrong_path.exists()}")
        
        old_cache.clear()
        if problematic_cache_dir.exists():
            shutil.rmtree(problematic_cache_dir)
            
    except Exception as e:
        print(f"  原始缓存管理器错误: {e}")
    
    # 优化的缓存管理器（已修复）
    print("\n✅ 优化缓存管理器:")
    try:
        new_cache = OptimizedBatchCacheManager(cache_dir=problematic_cache_dir, enable_file_cache=True)
        new_cache.set("test", {"test": "data"}, "clickhouse")
        new_cache.persist_cache_to_disk("clickhouse")
        
        print(f"  Brain API文件: {new_cache.brain_api_cache_file}")
        print(f"  ClickHouse文件: {new_cache.clickhouse_cache_file}")
        print(f"  ClickHouse文件存在: {new_cache.clickhouse_cache_file.exists()}")
        
        # 验证文件在正确位置
        correct_path = problematic_cache_dir / "clickhouse_cache.pkl"
        print(f"  正确路径存在: {correct_path.exists()}")
        
        new_cache.clear()
        if problematic_cache_dir.exists():
            shutil.rmtree(problematic_cache_dir)
            
    except Exception as e:
        print(f"  优化缓存管理器错误: {e}")

def test_memory_first_strategy():
    """测试内存优先策略"""
    print("\n🧠 测试内存优先策略...")
    
    cache_dir = Path("./test_memory_cache")
    cache_manager = OptimizedBatchCacheManager(cache_dir=cache_dir, enable_file_cache=True)
    
    # 测试数据
    test_smiles = ["CCO", "CCC", "CCCC", "CCCCC"]
    test_results = {smiles: {"value": f"result_{smiles}"} for smiles in test_smiles}
    
    # 1. 缓存到内存（不立即写入磁盘）
    print("  📝 缓存数据到内存...")
    batch_key = cache_manager._generate_batch_key(test_smiles)
    cache_manager.cache_batch_in_memory(batch_key, test_smiles, test_results, 'brain_api')
    
    # 验证内存中有数据
    cached_results, missing = cache_manager.get_cached_batch(batch_key, test_smiles, 'brain_api')
    print(f"  ✅ 内存缓存命中: {len(cached_results)}/{len(test_smiles)}")
    
    # 验证磁盘上还没有数据（除了初始化时的空文件）
    brain_file_size = cache_manager.brain_api_cache_file.stat().st_size if cache_manager.brain_api_cache_file.exists() else 0
    print(f"  📁 磁盘文件大小（持久化前）: {brain_file_size} bytes")
    
    # 2. 批量持久化到磁盘
    print("  💾 批量持久化到磁盘...")
    cache_manager.persist_cache_to_disk('brain_api')
    
    # 验证磁盘上现在有数据
    brain_file_size_after = cache_manager.brain_api_cache_file.stat().st_size
    print(f"  📁 磁盘文件大小（持久化后）: {brain_file_size_after} bytes")
    
    # 清理
    cache_manager.clear()
    if cache_dir.exists():
        shutil.rmtree(cache_dir)

def test_bulk_loading():
    """测试批量加载功能"""
    print("\n📥 测试批量加载功能...")
    
    cache_dir = Path("./test_bulk_load")
    
    # 第一阶段：创建缓存数据
    print("  📝 创建初始缓存数据...")
    cache_manager1 = OptimizedBatchCacheManager(cache_dir=cache_dir, enable_file_cache=True)
    
    test_data = {
        "brain_api": {"CCO": {"mw": 46}, "CCC": {"mw": 44}},
        "clickhouse": {"CCO": {"price": 10}, "CCC": {"price": 15}}
    }
    
    for cache_type, data in test_data.items():
        for smiles, result in data.items():
            cache_manager1.set(smiles, result, cache_type)
        cache_manager1.persist_cache_to_disk(cache_type)
    
    # 验证文件存在
    print(f"  📁 Brain API文件存在: {cache_manager1.brain_api_cache_file.exists()}")
    print(f"  📁 ClickHouse文件存在: {cache_manager1.clickhouse_cache_file.exists()}")
    
    # 第二阶段：新实例批量加载
    print("  🚀 创建新实例并批量加载...")
    cache_manager2 = OptimizedBatchCacheManager(cache_dir=cache_dir, enable_file_cache=True)
    
    # 验证数据已加载到内存
    for cache_type, data in test_data.items():
        for smiles, expected_result in data.items():
            cached_result = cache_manager2.get(smiles, cache_type)
            if cached_result == expected_result:
                print(f"  ✅ {cache_type}:{smiles} 批量加载成功")
            else:
                print(f"  ❌ {cache_type}:{smiles} 批量加载失败")
    
    # 清理
    cache_manager2.clear()
    if cache_dir.exists():
        shutil.rmtree(cache_dir)

def performance_comparison():
    """性能对比测试"""
    print("\n⚡ 性能对比测试...")
    
    # 测试数据
    test_smiles = [f"SMILES_{i}" for i in range(1000)]
    test_results = {smiles: {"value": f"result_{smiles}"} for smiles in test_smiles}
    
    # 原始缓存管理器测试
    print("  🐌 原始缓存管理器性能:")
    cache_dir_old = Path("./perf_test_old")
    old_cache = BatchCacheManager(cache_dir=cache_dir_old, enable_file_cache=True)
    
    start_time = time.time()
    for smiles, result in test_results.items():
        old_cache.set(f"brain_api:{smiles}", result)
    old_time = time.time() - start_time
    print(f"    写入时间: {old_time:.3f} 秒")
    
    start_time = time.time()
    for smiles in test_smiles:
        old_cache.get(f"brain_api:{smiles}")
    old_read_time = time.time() - start_time
    print(f"    读取时间: {old_read_time:.3f} 秒")
    
    old_cache.clear()
    if cache_dir_old.exists():
        shutil.rmtree(cache_dir_old)
    
    # 优化缓存管理器测试
    print("  🚀 优化缓存管理器性能:")
    cache_dir_new = Path("./perf_test_new")
    new_cache = OptimizedBatchCacheManager(cache_dir=cache_dir_new, enable_file_cache=True)
    
    start_time = time.time()
    # 批量缓存到内存
    batch_key = new_cache._generate_batch_key(test_smiles)
    new_cache.cache_batch_in_memory(batch_key, test_smiles, test_results, 'brain_api')
    # 批量持久化
    new_cache.persist_cache_to_disk('brain_api')
    new_time = time.time() - start_time
    print(f"    写入时间: {new_time:.3f} 秒")
    
    start_time = time.time()
    for smiles in test_smiles:
        new_cache.get(smiles, 'brain_api')
    new_read_time = time.time() - start_time
    print(f"    读取时间: {new_read_time:.3f} 秒")
    
    # 性能提升计算
    if old_time > 0 and new_time > 0:
        write_improvement = old_time / new_time
        read_improvement = old_read_time / new_read_time
        print(f"  📈 写入性能提升: {write_improvement:.1f}x")
        print(f"  📈 读取性能提升: {read_improvement:.1f}x")
    
    new_cache.clear()
    if cache_dir_new.exists():
        shutil.rmtree(cache_dir_new)

def run_all_tests():
    """运行所有测试"""
    print("🧪 优化缓存系统测试套件")
    print("=" * 50)
    
    test_file_path_bug_fix()
    test_memory_first_strategy()
    test_bulk_loading()
    performance_comparison()
    
    print("\n🎉 所有测试完成！")

if __name__ == "__main__":
    run_all_tests()
