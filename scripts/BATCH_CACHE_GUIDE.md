# 批量缓存系统使用指南

## 概述

新的批量缓存系统（`BatchCacheManager`）是对原有缓存系统的重大改进，专门针对API请求批次进行优化。该系统将整个API响应批次作为单个缓存单元，显著提高了缓存效率并简化了文件结构。

## 主要特性

### 1. 批量级别缓存
- **批次缓存**：将整个API响应批次作为单个单元进行缓存
- **部分命中支持**：当请求的SMILES列表部分存在于缓存中时，返回已缓存的结果并标识缺失的SMILES
- **智能批次键**：基于SMILES列表的排序哈希生成一致的批次键

### 2. 简化的文件结构
- **两文件架构**：仅使用两个合并缓存文件
  - `brain_api_cache.pkl` - 存储所有Brain API结果
  - `clickhouse_cache.pkl` - 存储所有ClickHouse结果
- **原子写入**：使用临时文件确保写入操作的原子性
- **文件锁定**：防止并发访问时的数据损坏

### 3. 向后兼容性
- 保持所有原有的缓存操作接口（`get`, `set`, `delete`, `clear`）
- 支持个体SMILES缓存和批量缓存的混合使用
- 平滑迁移路径，无需修改现有代码

## 核心API

### 批量缓存操作

#### `cache_batch(batch_key, smiles_list, results, cache_type='brain_api')`
缓存整个API响应批次。

```python
# 示例：缓存Brain API批次结果
smiles_list = ["CCO", "CCC", "CCCC"]
results = {
    "CCO": {"property1": 1.0, "property2": "value1"},
    "CCC": {"property1": 2.0, "property2": "value2"},
    "CCCC": {"property1": 3.0, "property2": "value3"}
}

batch_key = cache_manager._generate_batch_key(smiles_list)
success = cache_manager.cache_batch(batch_key, smiles_list, results, 'brain_api')
```

#### `get_cached_batch(batch_key, smiles_list, cache_type='brain_api')`
获取批量缓存结果，支持部分命中。

```python
# 返回：(缓存的结果字典, 缺失的SMILES列表)
cached_results, missing_smiles = cache_manager.get_cached_batch(
    batch_key, smiles_list, 'brain_api'
)

# 处理部分命中
if missing_smiles:
    # 对缺失的SMILES进行API调用
    new_results = call_api(missing_smiles)
    # 合并结果
    all_results = {**cached_results, **new_results}
```

#### `get_missing_smiles(smiles_list, cache_type='brain_api')`
获取未缓存的SMILES列表。

```python
missing_smiles = cache_manager.get_missing_smiles(smiles_list, 'brain_api')
if missing_smiles:
    # 只对缺失的SMILES进行API调用
    results = call_api(missing_smiles)
```

### 传统缓存操作（向后兼容）

```python
# 个体SMILES缓存
cache_manager.set("brain_api:CCO", {"property": 1.0})
result = cache_manager.get("brain_api:CCO")
cache_manager.delete("brain_api:CCO")

# 清空所有缓存
cache_manager.clear()
```

## 使用模式

### 1. 优化的API调用模式

```python
def optimized_api_call(smiles_list, cache_manager, cache_type='brain_api'):
    """优化的API调用，利用批量缓存"""
    
    # 生成批次键
    batch_key = cache_manager._generate_batch_key(smiles_list)
    
    # 检查批量缓存
    cached_results, missing_smiles = cache_manager.get_cached_batch(
        batch_key, smiles_list, cache_type
    )
    
    # 如果有缺失的SMILES，进行API调用
    if missing_smiles:
        new_results = call_external_api(missing_smiles)
        
        # 缓存新结果（可以选择缓存为新的批次或更新现有批次）
        if len(missing_smiles) == len(smiles_list):
            # 全新批次
            cache_manager.cache_batch(batch_key, smiles_list, new_results, cache_type)
        else:
            # 个体缓存缺失的SMILES
            for smiles, result in new_results.items():
                cache_manager.set(f"{cache_type}:{smiles}", result)
        
        # 合并结果
        all_results = {**cached_results, **new_results}
    else:
        all_results = cached_results
    
    return all_results
```

### 2. 渐进式缓存构建

```python
def progressive_caching(large_smiles_list, batch_size=1000):
    """渐进式构建大型缓存"""
    
    for i in range(0, len(large_smiles_list), batch_size):
        batch = large_smiles_list[i:i+batch_size]
        
        # 检查缺失的SMILES
        missing = cache_manager.get_missing_smiles(batch, 'brain_api')
        
        if missing:
            # 只对缺失的进行API调用
            results = call_api(missing)
            
            # 缓存结果
            batch_key = cache_manager._generate_batch_key(missing)
            cache_manager.cache_batch(batch_key, missing, results, 'brain_api')
```

## 性能优势

### 1. 减少文件系统开销
- **文件数量**：从数千个小文件减少到2个大文件
- **I/O操作**：批量读写减少磁盘I/O次数
- **文件系统压力**：显著降低inode使用和目录遍历开销

### 2. 提高API效率
- **批次重用**：相同SMILES组合的请求可以完全命中缓存
- **部分命中**：减少重复的API调用
- **智能缓存**：基于实际使用模式优化缓存策略

### 3. 内存优化
- **分层缓存**：内存缓存 + 文件缓存的两级架构
- **LRU淘汰**：基于访问时间的智能内存管理
- **压缩存储**：可选的gzip压缩减少存储空间

## 配置选项

```python
cache_manager = BatchCacheManager(
    cache_dir=Path('./cache'),
    memory_ttl=3600,           # 内存缓存TTL (秒)
    file_ttl=86400,            # 文件缓存TTL (秒)
    max_memory_entries=10000,  # 最大内存条目数
    enable_compression=True,   # 启用文件压缩
    enable_file_cache=True     # 启用文件缓存
)
```

## 迁移指南

### 从旧缓存系统迁移

1. **无缝替换**：
   ```python
   # 旧代码
   from cache_manager import CacheManager
   
   # 新代码（无需修改）
   from cache_manager import CacheManager  # 现在指向BatchCacheManager
   ```

2. **利用新功能**：
   ```python
   # 在现有代码中添加批量缓存支持
   if hasattr(cache_manager, 'cache_batch'):
       # 使用新的批量缓存功能
       batch_key = cache_manager._generate_batch_key(smiles_list)
       cached_results, missing = cache_manager.get_cached_batch(
           batch_key, smiles_list, 'brain_api'
       )
   else:
       # 回退到传统方式
       missing = [s for s in smiles_list if cache_manager.get(f"brain_api:{s}") is None]
   ```

## 监控和调试

### 缓存统计

```python
stats = cache_manager.get_stats()
print(f"命中率: {stats.hit_rate:.2%}")
print(f"完全命中率: {stats.full_hit_rate:.2%}")
print(f"内存条目: {stats.memory_entries}")
print(f"批量条目: {stats.batch_entries}")
print(f"文件条目: {stats.file_entries}")
```

### 缓存维护

```python
# 清理过期条目
cache_manager.cleanup_expired()

# 完全清空缓存
cache_manager.clear()

# 删除特定批次
cache_manager.delete_batch(batch_key, 'brain_api')
```

## 最佳实践

1. **批次大小**：建议使用1000-5000个SMILES的批次大小
2. **缓存键管理**：使用一致的SMILES排序确保缓存键的可重现性
3. **错误处理**：始终检查缓存操作的返回值
4. **定期清理**：定期运行`cleanup_expired()`清理过期条目
5. **监控使用**：定期检查缓存统计以优化配置

## 故障排除

### 常见问题

1. **缓存文件损坏**：系统会自动检测并重建损坏的缓存文件
2. **并发访问**：文件锁机制确保并发安全
3. **内存不足**：LRU淘汰机制自动管理内存使用
4. **磁盘空间**：可启用压缩减少存储需求

### 调试技巧

```python
import logging
logging.getLogger('cache_manager').setLevel(logging.DEBUG)
```

这将启用详细的缓存操作日志，帮助诊断问题。
