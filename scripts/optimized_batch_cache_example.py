#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的批量缓存系统集成示例

展示新的内存优先、批量持久化缓存策略：
1. 内存优先缓存：在流水线执行期间将所有缓存数据保存在内存中
2. 批量持久化：仅在完整流水线阶段结束时将缓存数据写入磁盘
3. 批量加载：在流水线启动时将整个缓存文件加载到内存中
4. 两个合并缓存文件：brain_api_cache.pkl 和 clickhouse_cache.pkl

修复了文件路径错误，提供更好的性能和更简洁的缓存管理。

作者: Assistant
日期: 2024
"""

import json
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
from optimized_cache_manager import OptimizedBatchCacheManager

class OptimizedAPIProcessor:
    """优化的API处理器，使用内存优先批量缓存"""
    
    def __init__(self, cache_dir: Path, batch_size: int = 1000):
        self.cache_manager = OptimizedBatchCacheManager(
            cache_dir=cache_dir,
            memory_ttl=3600,    # 1小时内存缓存
            file_ttl=86400,     # 24小时文件缓存
            max_memory_entries=50000,  # 增加内存容量
            enable_compression=True
        )
        self.batch_size = batch_size
        
    def process_smiles_batch(self, smiles_list: List[str], api_type: str = 'brain_api') -> Dict[str, Any]:
        """
        处理SMILES批次，利用优化的内存优先缓存
        """
        print(f"🔄 处理 {len(smiles_list)} 个SMILES ({api_type})")
        
        # 生成批次键
        batch_key = self.cache_manager._generate_batch_key(smiles_list)
        
        # 检查缓存（内存优先）
        cached_results, missing_smiles = self.cache_manager.get_cached_batch(
            batch_key, smiles_list, api_type
        )
        
        print(f"📊 缓存命中: {len(cached_results)}/{len(smiles_list)}")
        print(f"🔍 需要API调用: {len(missing_smiles)}")
        
        # 如果有缺失的SMILES，进行API调用
        if missing_smiles:
            print(f"🌐 调用 {api_type} API...")
            new_results = self._call_api(missing_smiles, api_type)
            
            # 缓存新结果到内存（不立即写入磁盘）
            if new_results:
                if len(missing_smiles) == len(smiles_list):
                    # 完整的新批次，使用批量缓存
                    self.cache_manager.cache_batch_in_memory(batch_key, smiles_list, new_results, api_type)
                    print(f"💾 内存批量缓存 {len(new_results)} 个结果")
                else:
                    # 部分结果，使用个体缓存
                    for smiles, result in new_results.items():
                        self.cache_manager.set(smiles, result, api_type)
                    print(f"💾 内存个体缓存 {len(new_results)} 个结果")
            
            # 合并结果
            all_results = {**cached_results, **new_results}
        else:
            all_results = cached_results
            print("✅ 完全命中缓存，无需API调用")
        
        return all_results
    
    def _call_api(self, smiles_list: List[str], api_type: str) -> Dict[str, Any]:
        """模拟API调用"""
        # 模拟API延迟
        time.sleep(0.05 * len(smiles_list) / 100)  # 减少延迟以展示性能
        
        results = {}
        for smiles in smiles_list:
            if api_type == 'brain_api':
                results[smiles] = {
                    "molecular_weight": len(smiles) * 10.5,
                    "logp": len(smiles) * 0.3,
                    "tpsa": len(smiles) * 5.2
                }
            elif api_type == 'clickhouse':
                results[smiles] = {
                    "price": len(smiles) * 1.5,
                    "availability": "in_stock",
                    "supplier": f"supplier_{len(smiles)}"
                }
        
        return results
    
    def process_large_dataset_optimized(self, smiles_list: List[str]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        使用优化策略处理大型数据集
        
        特点：
        1. 所有数据首先缓存在内存中
        2. 仅在阶段结束时批量写入磁盘
        3. 减少磁盘I/O操作
        """
        print(f"🚀 开始优化处理大型数据集: {len(smiles_list)} 个SMILES")
        print(f"📦 批次大小: {self.batch_size}")
        
        brain_results = {}
        clickhouse_results = {}
        
        # 阶段1：处理所有Brain API请求（内存缓存）
        print(f"\n🧠 阶段1: Brain API处理")
        for i in range(0, len(smiles_list), self.batch_size):
            batch = smiles_list[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(smiles_list) + self.batch_size - 1) // self.batch_size
            
            print(f"📦 Brain API批次 {batch_num}/{total_batches}")
            batch_brain_results = self.process_smiles_batch(batch, 'brain_api')
            brain_results.update(batch_brain_results)
        
        # 批量持久化Brain API缓存
        print(f"💾 批量持久化Brain API缓存到磁盘...")
        self.cache_manager.persist_cache_to_disk('brain_api')
        
        # 阶段2：处理所有ClickHouse请求（内存缓存）
        print(f"\n🏪 阶段2: ClickHouse处理")
        for i in range(0, len(smiles_list), self.batch_size):
            batch = smiles_list[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(smiles_list) + self.batch_size - 1) // self.batch_size
            
            print(f"📦 ClickHouse批次 {batch_num}/{total_batches}")
            batch_clickhouse_results = self.process_smiles_batch(batch, 'clickhouse')
            clickhouse_results.update(batch_clickhouse_results)
        
        # 批量持久化ClickHouse缓存
        print(f"💾 批量持久化ClickHouse缓存到磁盘...")
        self.cache_manager.persist_cache_to_disk('clickhouse')
        
        return brain_results, clickhouse_results
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = self.cache_manager.get_stats()
        return {
            "hit_rate": f"{stats.hit_rate:.2%}",
            "full_hit_rate": f"{stats.full_hit_rate:.2%}",
            "total_hits": stats.hits,
            "partial_hits": stats.partial_hits,
            "misses": stats.misses,
            "memory_entries": stats.memory_entries,
            "batch_entries": stats.batch_entries,
            "evictions": stats.evictions
        }

def demonstrate_optimized_caching():
    """演示优化的批量缓存系统"""
    print("🎯 优化批量缓存系统演示")
    print("=" * 50)
    
    # 创建处理器
    cache_dir = Path("./optimized_cache")
    processor = OptimizedAPIProcessor(cache_dir, batch_size=500)
    
    # 生成测试数据
    test_smiles = [
        "CCO", "CCC", "CCCC", "CCCCC", "CCCCCC",
        "C1CCCCC1", "c1ccccc1", "CCN", "CCC(=O)O", "CC(C)C",
        "CCCCO", "CCCCN", "CC(C)(C)C", "C1CCNCC1", "CC(=O)O"
    ] * 100  # 1500个SMILES用于演示
    
    print(f"📋 测试数据: {len(test_smiles)} 个SMILES")
    
    # 第一次运行（冷缓存）
    print("\n🥶 第一次运行（冷缓存）:")
    start_time = time.time()
    brain_results, clickhouse_results = processor.process_large_dataset_optimized(test_smiles)
    first_run_time = time.time() - start_time
    
    print(f"\n⏱️ 第一次运行耗时: {first_run_time:.2f} 秒")
    print(f"📊 Brain API结果: {len(brain_results)} 个")
    print(f"📊 ClickHouse结果: {len(clickhouse_results)} 个")
    
    # 显示缓存统计
    stats = processor.get_cache_statistics()
    print(f"\n📈 缓存统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 第二次运行（热缓存）
    print("\n🔥 第二次运行（热缓存）:")
    start_time = time.time()
    brain_results2, clickhouse_results2 = processor.process_large_dataset_optimized(test_smiles)
    second_run_time = time.time() - start_time
    
    print(f"\n⏱️ 第二次运行耗时: {second_run_time:.2f} 秒")
    if second_run_time > 0:
        print(f"⚡ 性能提升: {(first_run_time / second_run_time):.1f}x")
    else:
        print(f"⚡ 性能提升: 极快（几乎瞬时完成）")
    
    # 验证结果一致性
    assert brain_results == brain_results2, "Brain API结果不一致"
    assert clickhouse_results == clickhouse_results2, "ClickHouse结果不一致"
    print("✅ 结果一致性验证通过")
    
    # 最终缓存统计
    final_stats = processor.get_cache_statistics()
    print(f"\n📈 最终缓存统计:")
    for key, value in final_stats.items():
        print(f"  {key}: {value}")
    
    # 检查缓存文件
    print(f"\n📁 缓存文件:")
    for cache_file in [processor.cache_manager.brain_api_cache_file, processor.cache_manager.clickhouse_cache_file]:
        if cache_file.exists():
            size_mb = cache_file.stat().st_size / (1024 * 1024)
            print(f"  {cache_file}: {size_mb:.2f} MB")
    
    # 清理演示缓存
    processor.cache_manager.clear()
    if cache_dir.exists():
        import shutil
        shutil.rmtree(cache_dir)
    
    print("\n🎉 优化演示完成！")

if __name__ == "__main__":
    demonstrate_optimized_caching()
