#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流水线配置管理模块

提供统一的配置管理功能，支持从文件加载配置和运行时配置验证
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

@dataclass
class PipelineConfig:
    """流水线配置类"""
    
    # 文件路径配置
    input_file: Path
    output_dir: Path = Path('./output')
    temp_dir: Path = Path('./temp')
    log_dir: Path = Path('./logs')
    
    # 批处理配置
    brain_api_batch_size: int = 3000
    clickhouse_batch_size: int = 3000
    csv_batch_size: int = 50000
    
    # 连接配置
    clickhouse_host: str = 'ec2-161-189-187-79.cn-northwest-1.compute.amazonaws.com.cn'
    clickhouse_port: int = 9000
    brain_api_timeout: int = 300
    clickhouse_timeout: int = 60
    
    # 错误处理配置
    max_retries: int = 3
    retry_delay: int = 5
    continue_on_error: bool = True
    step_timeout: int = 3600  # 单步骤超时时间(秒)
    
    # 性能配置
    max_memory_mb: int = 8192  # 最大内存使用(MB)
    cleanup_temp: bool = False
    enable_cache: bool = True
    cache_dir: Path = Path('./cache')

    # 优化配置
    enable_optimizations: bool = True
    enable_smiles_deduplication: bool = True
    enable_persistent_cache: bool = True
    enable_concurrent_processing: bool = True

    # 缓存配置
    cache_memory_ttl: float = 3600  # 内存缓存TTL (秒)
    cache_file_ttl: float = 86400   # 文件缓存TTL (秒)
    max_memory_cache_entries: int = 10000
    enable_cache_compression: bool = True

    # 并发配置
    max_concurrent_requests: int = 1
    brain_api_timeout: int = 30
    clickhouse_timeout: int = 30
    concurrent_retry_attempts: int = 3
    concurrent_retry_delay: float = 1.0
    
    # 日志配置
    log_level: str = 'INFO'
    log_format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    enable_progress_bar: bool = True
    
    # 验证配置
    validate_input: bool = True
    validate_output: bool = True
    sample_validation_size: int = 1000
    
    def __post_init__(self):
        """初始化后的验证和设置"""
        # 确保路径是Path对象
        if isinstance(self.input_file, str):
            self.input_file = Path(self.input_file)
        if isinstance(self.output_dir, str):
            self.output_dir = Path(self.output_dir)
        if isinstance(self.temp_dir, str):
            self.temp_dir = Path(self.temp_dir)
        if isinstance(self.log_dir, str):
            self.log_dir = Path(self.log_dir)
        if isinstance(self.cache_dir, str):
            self.cache_dir = Path(self.cache_dir)
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        errors = []
        
        # 验证输入文件
        if not self.input_file.exists():
            errors.append(f"输入文件不存在: {self.input_file}")
        
        # 验证批处理大小
        if self.brain_api_batch_size <= 0:
            errors.append("Brain API批处理大小必须大于0")
        if self.clickhouse_batch_size <= 0:
            errors.append("ClickHouse批处理大小必须大于0")
        if self.csv_batch_size <= 0:
            errors.append("CSV批处理大小必须大于0")
        
        # 验证超时配置
        if self.brain_api_timeout <= 0:
            errors.append("Brain API超时时间必须大于0")
        if self.clickhouse_timeout <= 0:
            errors.append("ClickHouse超时时间必须大于0")
        if self.step_timeout <= 0:
            errors.append("步骤超时时间必须大于0")
        
        # 验证重试配置
        if self.max_retries < 0:
            errors.append("最大重试次数不能为负数")
        if self.retry_delay < 0:
            errors.append("重试延迟不能为负数")
        
        # 验证ClickHouse连接
        if not self._test_clickhouse_connection():
            errors.append(f"无法连接到ClickHouse服务器: {self.clickhouse_host}:{self.clickhouse_port}")
        
        # 验证内存配置
        if self.max_memory_mb <= 0:
            errors.append("最大内存使用必须大于0")

        # 验证优化配置
        if self.cache_memory_ttl <= 0:
            errors.append("内存缓存TTL必须大于0")
        if self.cache_file_ttl <= 0:
            errors.append("文件缓存TTL必须大于0")
        if self.max_memory_cache_entries <= 0:
            errors.append("最大内存缓存条目数必须大于0")
        if self.max_concurrent_requests <= 0:
            errors.append("最大并发请求数必须大于0")
        if self.concurrent_retry_attempts < 0:
            errors.append("并发重试次数不能为负数")
        if self.concurrent_retry_delay < 0:
            errors.append("并发重试延迟不能为负数")

        # 验证日志级别
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.log_level.upper() not in valid_log_levels:
            errors.append(f"无效的日志级别: {self.log_level}，有效值: {valid_log_levels}")

        # 验证端口号
        if not (1 <= self.clickhouse_port <= 65535):
            errors.append(f"无效的ClickHouse端口号: {self.clickhouse_port}")
        
        if errors:
            print("❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True
    
    def load_from_file(self, config_file: str) -> bool:
        """从文件加载配置"""
        config_path = Path(config_file)
        
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {config_file}")
            return False
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    config_data = json.load(f)
                else:
                    print(f"❌ 不支持的配置文件格式: {config_path.suffix}")
                    return False
            
            # 更新配置
            self._update_from_dict(config_data)
            print(f"✅ 已加载配置文件: {config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            return False
    
    def save_to_file(self, config_file: str, format: str = 'yaml') -> bool:
        """保存配置到文件"""
        config_path = Path(config_file)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            config_dict = self._to_serializable_dict()
            
            with open(config_path, 'w', encoding='utf-8') as f:
                if format.lower() in ['yaml', 'yml']:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
                elif format.lower() == 'json':
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
                else:
                    print(f"❌ 不支持的保存格式: {format}")
                    return False
            
            print(f"✅ 配置已保存到: {config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存配置文件失败: {str(e)}")
            return False
    
    def _update_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in config_data.items():
            if hasattr(self, key):
                # 处理Path类型
                if key.endswith('_dir') or key.endswith('_file'):
                    setattr(self, key, Path(value))
                else:
                    setattr(self, key, value)
    
    def _to_serializable_dict(self) -> Dict[str, Any]:
        """转换为可序列化的字典"""
        config_dict = asdict(self)
        
        # 将Path对象转换为字符串
        for key, value in config_dict.items():
            if isinstance(value, Path):
                config_dict[key] = str(value)
        
        return config_dict
    
    def print_config(self):
        """打印当前配置"""
        print("📋 当前流水线配置:")
        print("=" * 50)
        
        print("📁 文件路径配置:")
        print(f"  输入文件: {self.input_file}")
        print(f"  输出目录: {self.output_dir}")
        print(f"  临时目录: {self.temp_dir}")
        print(f"  日志目录: {self.log_dir}")
        print(f"  缓存目录: {self.cache_dir}")
        
        print("\n⚙️ 批处理配置:")
        print(f"  Brain API批大小: {self.brain_api_batch_size:,}")
        print(f"  ClickHouse批大小: {self.clickhouse_batch_size:,}")
        print(f"  CSV批大小: {self.csv_batch_size:,}")
        
        print("\n🔗 连接配置:")
        print(f"  ClickHouse主机: {self.clickhouse_host}:{self.clickhouse_port}")
        print(f"  Brain API超时: {self.brain_api_timeout}秒")
        print(f"  ClickHouse超时: {self.clickhouse_timeout}秒")
        
        print("\n🛠️ 错误处理配置:")
        print(f"  最大重试次数: {self.max_retries}")
        print(f"  重试延迟: {self.retry_delay}秒")
        print(f"  出错时继续: {self.continue_on_error}")
        print(f"  步骤超时: {self.step_timeout}秒")
        
        print("\n🚀 性能配置:")
        print(f"  最大内存: {self.max_memory_mb} MB")
        print(f"  清理临时文件: {self.cleanup_temp}")
        print(f"  启用缓存: {self.enable_cache}")

        print("\n⚡ 优化配置:")
        print(f"  启用优化: {self.enable_optimizations}")
        print(f"  SMILES去重: {self.enable_smiles_deduplication}")
        print(f"  持久化缓存: {self.enable_persistent_cache}")
        print(f"  并发处理: {self.enable_concurrent_processing}")

        print("\n💾 缓存配置:")
        print(f"  内存缓存TTL: {self.cache_memory_ttl}秒")
        print(f"  文件缓存TTL: {self.cache_file_ttl}秒")
        print(f"  最大内存缓存条目: {self.max_memory_cache_entries:,}")
        print(f"  启用压缩: {self.enable_cache_compression}")

        print("\n🔄 并发配置:")
        print(f"  最大并发请求: {self.max_concurrent_requests}")
        print(f"  重试次数: {self.concurrent_retry_attempts}")
        print(f"  重试延迟: {self.concurrent_retry_delay}秒")
        
        print("\n📝 日志配置:")
        print(f"  日志级别: {self.log_level}")
        print(f"  显示进度条: {self.enable_progress_bar}")
        
        print("\n✅ 验证配置:")
        print(f"  验证输入: {self.validate_input}")
        print(f"  验证输出: {self.validate_output}")
        print(f"  抽样验证大小: {self.sample_validation_size:,}")
        
        print("=" * 50)
    
    def get_memory_limit_bytes(self) -> int:
        """获取内存限制(字节)"""
        return self.max_memory_mb * 1024 * 1024
    
    def is_memory_limit_exceeded(self, current_memory_bytes: int) -> bool:
        """检查是否超过内存限制"""
        return current_memory_bytes > self.get_memory_limit_bytes()
    
    def get_step_config(self, step_name: str) -> Dict[str, Any]:
        """获取特定步骤的配置"""
        step_configs = {
            'convert_jsonl_to_csv': {
                'batch_size': self.csv_batch_size,
                'timeout': self.step_timeout,
                'validate_input': self.validate_input
            },
            'update_csv_with_brain_api': {
                'batch_size': self.brain_api_batch_size,
                'timeout': self.brain_api_timeout,
                'max_retries': self.max_retries,
                'retry_delay': self.retry_delay
            },
            'update_csv_with_clickhouse': {
                'batch_size': self.clickhouse_batch_size,
                'timeout': self.clickhouse_timeout,
                'host': self.clickhouse_host,
                'port': self.clickhouse_port,
                'max_retries': self.max_retries
            },
            'deduplicate_and_update_prices': {
                'timeout': self.step_timeout,
                'validate_output': self.validate_output
            }
        }
        
        return step_configs.get(step_name, {})
    
    def _test_clickhouse_connection(self) -> bool:
        """测试ClickHouse连接"""
        try:
            from clickhouse_driver import Client
            client = Client(
                host=self.clickhouse_host,
                port=self.clickhouse_port,
                connect_timeout=5,
                send_receive_timeout=10
            )
            # 执行简单查询测试连接
            client.execute('SELECT 1')
            return True
        except Exception as e:
            print(f"⚠️ ClickHouse连接测试失败: {e}")
            return False

def create_default_config_file(config_file: str = 'pipeline_config.yaml') -> bool:
    """创建默认配置文件"""
    default_config = PipelineConfig(
        input_file=Path('data/input.jsonl')
    )
    
    return default_config.save_to_file(config_file)

def load_config_from_file(config_file: str, input_file: Optional[str] = None) -> Optional[PipelineConfig]:
    """从文件加载配置"""
    if input_file:
        config = PipelineConfig(input_file=Path(input_file))
    else:
        config = PipelineConfig(input_file=Path('data/input.jsonl'))
    
    if config.load_from_file(config_file):
        if config.validate():
            return config
        else:
            return None
    else:
        return None

if __name__ == "__main__":
    # 创建示例配置文件
    print("创建示例配置文件...")
    if create_default_config_file('example_config.yaml'):
        print("✅ 示例配置文件已创建: example_config.yaml")
    else:
        print("❌ 创建示例配置文件失败")