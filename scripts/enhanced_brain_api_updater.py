#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的Brain API更新器

集成SMILES去重、持久化缓存、并发处理和错误重试机制，
在保持向后兼容性的同时提供显著的性能优化。

作者: Assistant
日期: 2024
"""

import sys
import os
import asyncio
import logging
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# 添加brain_server_client模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'brain_server_client'))

try:
    from brain_server_client import BrainServerClient, SmilesResult
except ImportError:
    print("错误: 无法导入brain_server_client模块")
    print("请确保brain_server_client目录在正确位置")
    sys.exit(1)

# 导入优化模块
from optimized_cache_manager import OptimizedBatchCacheManager as CacheManager
from smiles_deduplicator import SmilesDeduplicator
from batch_processor import BatchProcessor, BatchConfig
from concurrent_processor import ConcurrentProcessor, ConcurrentConfig
from pipeline_config import PipelineConfig


class EnhancedBrainAPIUpdater:
    """增强的Brain API更新器"""
    
    def __init__(
        self,
        config: PipelineConfig,
        enable_optimizations: bool = True
    ):
        self.config = config
        self.enable_optimizations = enable_optimizations and config.enable_optimizations
        self.logger = logging.getLogger(__name__)
        
        # Brain API客户端
        self.client = BrainServerClient()
        
        # 优化组件
        if self.enable_optimizations:
            self._init_optimization_components()
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'cache_hits': 0,
            'api_calls': 0,
            'deduplication_savings': 0,
            'errors': 0
        }
    
    def _init_optimization_components(self):
        """初始化优化组件"""
        # 缓存管理器
        if self.config.enable_persistent_cache:
            self.cache_manager = CacheManager(
                cache_dir=self.config.cache_dir / 'brain_api',
                memory_ttl=self.config.cache_memory_ttl,
                file_ttl=self.config.cache_file_ttl,
                max_memory_entries=self.config.max_memory_cache_entries,
                enable_compression=self.config.enable_cache_compression,
                enable_file_cache=True
            )
        else:
            self.cache_manager = None
        
        # SMILES去重器
        if self.config.enable_smiles_deduplication:
            self.deduplicator = SmilesDeduplicator('canonical_smiles')
        else:
            self.deduplicator = None
        
        # 批处理器
        batch_config = BatchConfig(
            batch_size=self.config.brain_api_batch_size,
            max_concurrent=self.config.max_concurrent_requests,
            retry_attempts=self.config.concurrent_retry_attempts,
            retry_delay=self.config.concurrent_retry_delay,
            timeout=self.config.brain_api_timeout
        )
        self.batch_processor = BatchProcessor(batch_config)
        
        # 并发处理器
        concurrent_config = ConcurrentConfig(
            max_concurrent=self.config.max_concurrent_requests,
            timeout=self.config.brain_api_timeout,
            retry_attempts=self.config.concurrent_retry_attempts,
            retry_delay=self.config.concurrent_retry_delay
        )
        self.concurrent_processor = ConcurrentProcessor(concurrent_config)
        
        self.logger.info("优化组件初始化完成")
    
    async def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """
        更新DataFrame中的SMILES数据
        
        Args:
            df: 输入的DataFrame，必须包含canonical_smiles列
            
        Returns:
            Tuple[pd.DataFrame, List[Dict]]: (成功更新的DataFrame, 错误记录列表)
        """
        if 'canonical_smiles' not in df.columns:
            raise ValueError("DataFrame中缺少canonical_smiles字段")
        
        self.logger.info(f"开始更新DataFrame中的SMILES数据，共 {len(df)} 行")
        self.stats['total_processed'] = len(df)
        
        if self.enable_optimizations:
            return await self._update_dataframe_optimized(df)
        else:
            return await self._update_dataframe_standard(df)
    
    async def _update_dataframe_optimized(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """优化的DataFrame更新方法"""
        self.logger.info("使用优化模式处理DataFrame")
        
        # 第一步：SMILES去重
        if self.deduplicator:
            unique_smiles, smiles_mapping = self.deduplicator.deduplicate_dataframe(df)
            self.stats['deduplication_savings'] = len(df) - len(unique_smiles)
            self.logger.info(f"去重节省API调用: {self.stats['deduplication_savings']}")
        else:
            unique_smiles = df['canonical_smiles'].dropna().unique().tolist()
            smiles_mapping = {smiles: [i for i, s in enumerate(df['canonical_smiles']) if s == smiles] 
                            for smiles in unique_smiles}
        
        # 第二步：检查缓存
        cached_results = {}
        uncached_smiles = []
        
        if self.cache_manager:
            for smiles in unique_smiles:
                cached_result = self.cache_manager.get(smiles, "brain_api")
                if cached_result:
                    cached_results[smiles] = cached_result
                    self.stats['cache_hits'] += 1
                else:
                    uncached_smiles.append(smiles)
            
            self.logger.info(f"缓存命中: {len(cached_results)}, 需要API调用: {len(uncached_smiles)}")
        else:
            uncached_smiles = unique_smiles
        
        # 第三步：批量处理未缓存的SMILES
        api_results = {}
        if uncached_smiles:
            self.stats['api_calls'] = len(uncached_smiles)
            api_results = await self._process_smiles_batch(uncached_smiles)
            
            # 将结果保存到缓存
            if self.cache_manager:
                for smiles, result in api_results.items():
                    self.cache_manager.set(smiles, result, "brain_api")
        
        # 第四步：合并结果
        all_results = {**cached_results, **api_results}
        
        # 第五步：重构DataFrame
        if self.deduplicator:
            result_df = self.deduplicator.reconstruct_results(all_results, df)
        else:
            result_df = self._reconstruct_dataframe_manual(df, all_results)
        
        # 分离成功和错误记录
        success_df = result_df[result_df['canonical_smiles'].notna() & 
                              result_df['inchified_smiles'].notna()].copy()
        error_df = result_df[result_df['canonical_smiles'].isna() | 
                            result_df['inchified_smiles'].isna()].copy()
        
        error_records = error_df.to_dict('records') if not error_df.empty else []
        
        self.logger.info(f"优化处理完成: 成功 {len(success_df)}, 错误 {len(error_records)}")
        self._log_optimization_stats()

        # 批量持久化Brain API缓存
        if self.cache_manager:
            self.cache_manager.persist_cache_to_disk('brain_api')
            self.logger.info("Brain API缓存已批量持久化到磁盘")

        return success_df, error_records
    
    async def _update_dataframe_standard(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """标准的DataFrame更新方法（向后兼容）"""
        self.logger.info("使用标准模式处理DataFrame")
        
        # 使用原始的批处理逻辑
        success_data = []
        error_data = []
        
        batch_size = self.config.brain_api_batch_size
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i + batch_size]
            batch = batch_df.to_dict('records')
            
            success_rows, error_rows = await self._process_batch_standard(batch)
            success_data.extend(success_rows)
            error_data.extend(error_rows)
        
        success_df = pd.DataFrame(success_data) if success_data else pd.DataFrame()
        error_records = error_data

        # 批量持久化Brain API缓存
        if self.cache_manager:
            self.cache_manager.persist_cache_to_disk('brain_api')
            self.logger.info("Brain API缓存已批量持久化到磁盘")

        return success_df, error_records
    
    async def _process_smiles_batch(self, smiles_list: List[str]) -> Dict[str, Dict[str, Any]]:
        """批量处理SMILES列表"""
        if not smiles_list:
            return {}
        
        try:
            # 调用Brain API
            results_list = await self.client.process_smiles(smiles_list)
            
            # 构建结果映射
            results_dict = {}
            for smiles, result in zip(smiles_list, results_list):
                if result and result.canonical_smiles and result.inchified_smiles:
                    results_dict[smiles] = {
                        'canonical_smiles': result.canonical_smiles,
                        'inchified_smiles': result.inchified_smiles
                    }
                else:
                    results_dict[smiles] = {
                        'canonical_smiles': None,
                        'inchified_smiles': None,
                        'error_reason': f'API返回无效SMILES: canonical={result.canonical_smiles if result else None}, inchified={result.inchified_smiles if result else None}'
                    }
            
            return results_dict
            
        except Exception as e:
            self.logger.error(f"批量处理SMILES失败: {e}")
            # 返回所有SMILES的错误结果
            return {
                smiles: {
                    'canonical_smiles': None,
                    'inchified_smiles': None,
                    'error_reason': f'API调用失败: {str(e)}'
                }
                for smiles in smiles_list
            }
    
    async def _process_batch_standard(self, batch: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """标准批处理方法（向后兼容）"""
        if not batch:
            return [], []

        rows_to_process = []
        error_rows = []
        success_rows = []

        # 分类处理行
        for row in batch:
            canonical_smiles = row.get('canonical_smiles', '').strip()
            if canonical_smiles == 'CC':
                row['canonical_smiles'] = 'CC'
                row['inchified_smiles'] = 'CC'
                success_rows.append(row)
            elif canonical_smiles:
                rows_to_process.append(row)
            else:
                row['error_reason'] = 'canonical_smiles字段为空'
                error_rows.append(row)

        if not rows_to_process:
            return success_rows, error_rows

        # 提取需要API处理的SMILES，并去重
        unique_smiles = list(set(r['canonical_smiles'] for r in rows_to_process))

        try:
            # 调用API处理唯一的SMILES
            results_list = await self.client.process_smiles(unique_smiles)
            
            # 将结果映射回SMILES字符串
            smiles_to_result = {smiles: result for smiles, result in zip(unique_smiles, results_list)}

            # 处理所有待处理的行
            for row in rows_to_process:
                smiles = row['canonical_smiles']
                result = smiles_to_result.get(smiles)

                if not result or not result.canonical_smiles or not result.inchified_smiles:
                    # API返回None或空字符串表示无效SMILES
                    row['error_reason'] = f'API返回无效SMILES: canonical={result.canonical_smiles if result else None}, inchified={result.inchified_smiles if result else None}'
                    error_rows.append(row)
                else:
                    # 更新SMILES字段
                    row['canonical_smiles'] = result.canonical_smiles
                    row['inchified_smiles'] = result.inchified_smiles
                    success_rows.append(row)

        except Exception as e:
            # API调用失败，所有待处理行都标记为错误
            for row in rows_to_process:
                row['error_reason'] = f'API调用失败: {str(e)}'
                error_rows.append(row)

        return success_rows, error_rows
    
    def _reconstruct_dataframe_manual(self, original_df: pd.DataFrame, results: Dict[str, Dict[str, Any]]) -> pd.DataFrame:
        """手动重构DataFrame（当去重器未启用时）"""
        result_df = original_df.copy()
        
        for idx, row in result_df.iterrows():
            smiles = row['canonical_smiles']
            if smiles in results:
                result_data = results[smiles]
                for column, value in result_data.items():
                    result_df.at[idx, column] = value
        
        return result_df
    
    def _log_optimization_stats(self):
        """记录优化统计信息"""
        self.logger.info("优化统计信息:")
        self.logger.info(f"  总处理数量: {self.stats['total_processed']:,}")
        self.logger.info(f"  缓存命中: {self.stats['cache_hits']:,}")
        self.logger.info(f"  API调用: {self.stats['api_calls']:,}")
        self.logger.info(f"  去重节省: {self.stats['deduplication_savings']:,}")
        
        if self.stats['total_processed'] > 0:
            cache_hit_rate = self.stats['cache_hits'] / self.stats['total_processed']
            api_reduction = (self.stats['total_processed'] - self.stats['api_calls']) / self.stats['total_processed']
            self.logger.info(f"  缓存命中率: {cache_hit_rate:.2%}")
            self.logger.info(f"  API调用减少: {api_reduction:.2%}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        if self.enable_optimizations:
            if self.cache_manager:
                cache_stats = self.cache_manager.get_stats()
                stats['cache_stats'] = {
                    'hit_rate': cache_stats.hit_rate,
                    'memory_entries': cache_stats.memory_entries,
                    'file_entries': cache_stats.file_entries
                }
            
            if self.deduplicator:
                dedup_stats = self.deduplicator.get_stats()
                stats['deduplication_stats'] = {
                    'total_smiles': dedup_stats.total_smiles,
                    'unique_smiles': dedup_stats.unique_smiles,
                    'reduction_ratio': dedup_stats.reduction_ratio
                }
        
        return stats
