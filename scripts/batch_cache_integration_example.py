#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量缓存系统集成示例

展示如何将新的批量缓存系统集成到现有的数据处理流水线中，
实现高效的API调用和缓存管理。

作者: Assistant
日期: 2024
"""

import json
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
from optimized_cache_manager import OptimizedBatchCacheManager

class OptimizedAPIProcessor:
    """优化的API处理器，使用批量缓存"""
    
    def __init__(self, cache_dir: Path, batch_size: int = 1000):
        self.cache_manager = OptimizedBatchCacheManager(
            cache_dir=cache_dir,
            memory_ttl=3600,    # 1小时内存缓存
            file_ttl=86400,     # 24小时文件缓存
            enable_compression=True
        )
        self.batch_size = batch_size
        
    def process_smiles_batch(self, smiles_list: List[str], api_type: str = 'brain_api') -> Dict[str, Any]:
        """
        处理SMILES批次，利用批量缓存优化API调用
        
        Args:
            smiles_list: SMILES字符串列表
            api_type: API类型 ('brain_api' 或 'clickhouse')
            
        Returns:
            SMILES -> 结果的字典
        """
        print(f"🔄 处理 {len(smiles_list)} 个SMILES ({api_type})")
        
        # 生成批次键
        batch_key = self.cache_manager._generate_batch_key(smiles_list)
        
        # 检查批量缓存
        cached_results, missing_smiles = self.cache_manager.get_cached_batch(
            batch_key, smiles_list, api_type
        )
        
        print(f"📊 缓存命中: {len(cached_results)}/{len(smiles_list)}")
        print(f"🔍 需要API调用: {len(missing_smiles)}")
        
        # 如果有缺失的SMILES，进行API调用
        if missing_smiles:
            print(f"🌐 调用 {api_type} API...")
            new_results = self._call_api(missing_smiles, api_type)
            
            # 缓存新结果到内存
            if new_results:
                # 如果是完整的新批次，使用批量缓存
                if len(missing_smiles) == len(smiles_list):
                    self.cache_manager.cache_batch_in_memory(batch_key, smiles_list, new_results, api_type)
                    print(f"💾 内存批量缓存 {len(new_results)} 个结果")
                else:
                    # 否则使用个体缓存
                    for smiles, result in new_results.items():
                        self.cache_manager.set(smiles, result, api_type)
                    print(f"💾 内存个体缓存 {len(new_results)} 个结果")
            
            # 合并结果
            all_results = {**cached_results, **new_results}
        else:
            all_results = cached_results
            print("✅ 完全命中缓存，无需API调用")
        
        return all_results
    
    def _call_api(self, smiles_list: List[str], api_type: str) -> Dict[str, Any]:
        """
        模拟API调用（在实际使用中替换为真实的API调用）
        """
        # 模拟API延迟
        time.sleep(0.1 * len(smiles_list) / 100)  # 模拟网络延迟
        
        results = {}
        for smiles in smiles_list:
            if api_type == 'brain_api':
                # 模拟Brain API响应
                results[smiles] = {
                    "molecular_weight": len(smiles) * 10.5,  # 模拟分子量
                    "logp": len(smiles) * 0.3,               # 模拟logP值
                    "tpsa": len(smiles) * 5.2                # 模拟TPSA值
                }
            elif api_type == 'clickhouse':
                # 模拟ClickHouse响应
                results[smiles] = {
                    "price": len(smiles) * 1.5,              # 模拟价格
                    "availability": "in_stock",              # 模拟库存状态
                    "supplier": f"supplier_{len(smiles)}"    # 模拟供应商
                }
        
        return results
    
    def process_large_dataset(self, smiles_list: List[str]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        处理大型数据集，使用优化的批量缓存策略

        Returns:
            (brain_api_results, clickhouse_results)
        """
        print(f"🚀 开始处理大型数据集: {len(smiles_list)} 个SMILES")
        print(f"📦 批次大小: {self.batch_size}")

        brain_results = {}
        clickhouse_results = {}

        # 阶段1: 处理所有Brain API请求（内存缓存）
        print(f"\n🧠 阶段1: Brain API处理")
        for i in range(0, len(smiles_list), self.batch_size):
            batch = smiles_list[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(smiles_list) + self.batch_size - 1) // self.batch_size

            print(f"📦 Brain API批次 {batch_num}/{total_batches}")
            batch_brain_results = self.process_smiles_batch(batch, 'brain_api')
            brain_results.update(batch_brain_results)

        # 批量持久化Brain API缓存
        print(f"💾 批量持久化Brain API缓存到磁盘...")
        self.cache_manager.persist_cache_to_disk('brain_api')

        # 阶段2: 处理所有ClickHouse请求（内存缓存）
        print(f"\n🏪 阶段2: ClickHouse处理")
        for i in range(0, len(smiles_list), self.batch_size):
            batch = smiles_list[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(smiles_list) + self.batch_size - 1) // self.batch_size

            print(f"📦 ClickHouse批次 {batch_num}/{total_batches}")
            batch_clickhouse_results = self.process_smiles_batch(batch, 'clickhouse')
            clickhouse_results.update(batch_clickhouse_results)

        # 批量持久化ClickHouse缓存
        print(f"💾 批量持久化ClickHouse缓存到磁盘...")
        self.cache_manager.persist_cache_to_disk('clickhouse')

        return brain_results, clickhouse_results
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = self.cache_manager.get_stats()
        return {
            "hit_rate": f"{stats.hit_rate:.2%}",
            "full_hit_rate": f"{stats.full_hit_rate:.2%}",
            "total_hits": stats.hits,
            "partial_hits": stats.partial_hits,
            "misses": stats.misses,
            "memory_entries": stats.memory_entries,
            "batch_entries": stats.batch_entries,
            "file_entries": stats.file_entries,
            "evictions": stats.evictions
        }

def demonstrate_batch_caching():
    """演示批量缓存系统的使用"""
    print("🎯 批量缓存系统演示")
    print("=" * 50)
    
    # 创建处理器
    cache_dir = Path("./demo_cache")
    processor = OptimizedAPIProcessor(cache_dir, batch_size=500)
    
    # 生成测试数据
    test_smiles = [
        "CCO", "CCC", "CCCC", "CCCCC", "CCCCCC",
        "C1CCCCC1", "c1ccccc1", "CCN", "CCC(=O)O", "CC(C)C",
        "CCCCO", "CCCCN", "CC(C)(C)C", "C1CCNCC1", "CC(=O)O"
    ] * 100  # 1500个SMILES用于演示
    
    print(f"📋 测试数据: {len(test_smiles)} 个SMILES")
    
    # 第一次运行（冷缓存）
    print("\n🥶 第一次运行（冷缓存）:")
    start_time = time.time()
    brain_results, clickhouse_results = processor.process_large_dataset(test_smiles)
    first_run_time = time.time() - start_time
    
    print(f"\n⏱️ 第一次运行耗时: {first_run_time:.2f} 秒")
    print(f"📊 Brain API结果: {len(brain_results)} 个")
    print(f"📊 ClickHouse结果: {len(clickhouse_results)} 个")
    
    # 显示缓存统计
    stats = processor.get_cache_statistics()
    print(f"\n📈 缓存统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 第二次运行（热缓存）
    print("\n🔥 第二次运行（热缓存）:")
    start_time = time.time()
    brain_results2, clickhouse_results2 = processor.process_large_dataset(test_smiles)
    second_run_time = time.time() - start_time
    
    print(f"\n⏱️ 第二次运行耗时: {second_run_time:.2f} 秒")
    print(f"⚡ 性能提升: {(first_run_time / second_run_time):.1f}x")
    
    # 验证结果一致性
    assert brain_results == brain_results2, "Brain API结果不一致"
    assert clickhouse_results == clickhouse_results2, "ClickHouse结果不一致"
    print("✅ 结果一致性验证通过")
    
    # 最终缓存统计
    final_stats = processor.get_cache_statistics()
    print(f"\n📈 最终缓存统计:")
    for key, value in final_stats.items():
        print(f"  {key}: {value}")
    
    # 清理演示缓存
    processor.cache_manager.clear()
    if cache_dir.exists():
        import shutil
        shutil.rmtree(cache_dir)
    
    print("\n🎉 演示完成！")

def integration_example():
    """集成示例：展示如何在现有代码中使用批量缓存"""
    print("\n🔧 集成示例")
    print("=" * 30)
    
    # 模拟现有的处理函数
    def process_smiles_with_cache(smiles_list: List[str], cache_manager):
        """使用缓存的SMILES处理函数"""
        results = {}
        
        # 检查缺失的SMILES
        missing_smiles = cache_manager.get_missing_smiles(smiles_list, 'brain_api')
        
        if missing_smiles:
            print(f"🔍 发现 {len(missing_smiles)} 个未缓存的SMILES")
            
            # 模拟API调用
            for smiles in missing_smiles:
                result = {"property": f"value_for_{smiles}"}
                cache_manager.set(smiles, result, "brain_api")
                results[smiles] = result

        # 获取所有结果
        for smiles in smiles_list:
            if smiles not in results:
                results[smiles] = cache_manager.get(smiles, "brain_api")
        
        return results
    
    # 使用示例
    cache_dir = Path("./integration_cache")
    cache_manager = OptimizedBatchCacheManager(cache_dir)
    
    test_smiles = ["CCO", "CCC", "CCCC"]
    results = process_smiles_with_cache(test_smiles, cache_manager)
    
    print(f"✅ 处理完成: {len(results)} 个结果")
    
    # 清理
    cache_manager.clear()
    if cache_dir.exists():
        import shutil
        shutil.rmtree(cache_dir)

if __name__ == "__main__":
    demonstrate_batch_caching()
    integration_example()
